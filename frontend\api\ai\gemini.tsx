// If using react-native-dotenv, use the following import:
import { API_KEY } from '../../';

/**
 * Calls the Gemini API to analyze an image and extract data.
 * @param {string} base64Data - The base64 encoded string of the image.
 * @param {string} mimeType - The MIME type of the image (e.g., 'image/jpeg').
 * @param {string} prompt - The instruction prompt for the AI model.
 * @returns {Promise<object>} A promise that resolves to the parsed JSON data from the AI.
 * @throws {Error} Throws an error if the API call fails or the response is invalid.
 */
export const callGeminiAPI = async (
    base64Data: string,
    mimeType: string,
    prompt: string
): Promise<any> => {
    // Check if the API key is available or is still the placeholder.
    if (!API_KEY || API_KEY === 'YOUR_API_KEY_HERE') {
        throw new Error(
            'API key is missing or is still the placeholder. Please update your .env file.'
        );
    }

    // The API endpoint for the gemini-2.0-flash model
    const apiUrl = `https://generativelanguage.googleapis.com/v1beta/models/gemini-2.0-flash:generateContent?key=${API_KEY}`;

    // Construct the request payload in the format required by the Gemini API
    const payload = {
        contents: [
            {
                parts: [
                    // The prompt instructing the model what to do
                    { text: prompt },
                    // The image data, sent as an inline base64 string
                    {
                        inlineData: {
                            mimeType: mimeType,
                            data: base64Data,
                        },
                    },
                ],
            },
        ],
        // Configuration to ensure the AI responds with a JSON object
        generationConfig: {
            responseMimeType: 'application/json',
        },
    };

    try {
        // Make the POST request to the API
        const response = await fetch(apiUrl, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify(payload),
        });

        // If the response is not OK (e.g., 4xx or 5xx status), throw an error
        if (!response.ok) {
            const errorBody = await response.json();
            console.error('API Error Response:', errorBody);
            // Check for a specific error message, otherwise provide a generic one
            const errorMessage =
                errorBody.error?.message ||
                'An unknown error occurred with the API.';
            if (errorMessage.includes('API key not valid')) {
                throw new Error(
                    'The configured API key is not valid. Please check your .env file.'
                );
            }
            throw new Error(`API Error: ${errorMessage}`);
        }

        const result = await response.json();

        // Extract the text content from the first candidate in the response
        const aiTextResponse =
            result.candidates?.[0]?.content?.parts?.[0]?.text;

        if (!aiTextResponse) {
            console.error('Invalid AI Response Structure:', result);
            throw new Error(
                'AI response was empty or in an unexpected format.'
            );
        }

        // Parse the JSON string from the AI into a JavaScript object
        return JSON.parse(aiTextResponse);
    } catch (error) {
        console.error('Error calling Gemini API:', error);
        // Re-throw the error to be caught by the component
        throw error;
    }
};
