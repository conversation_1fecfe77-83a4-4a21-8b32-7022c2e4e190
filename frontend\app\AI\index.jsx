import React, { useState, useMemo } from 'react';
import {
    StyleSheet,
    Text,
    View,
    TouchableOpacity,
    Modal,
    TextInput,
    SafeAreaView,
    ScrollView,
    ActivityIndicator,
    Platform,
    Dimensions,
} from 'react-native';
import * as ImagePicker from 'expo-image-picker';
import { Svg, Path } from 'react-native-svg';
import { callGeminiAPI } from '../../api/ai/gemini'; // Importing from the TypeScript file

// --- Icon Components ---
const CheckCircleIcon = () => (
    <Svg
        style={styles.resultDetailIcon}
        viewBox="0 0 24 24"
        fill="none"
        stroke="#22c55e"
    >
        <Path
            strokeLinecap="round"
            strokeLinejoin="round"
            strokeWidth="2"
            d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"
        />
    </Svg>
);
const XCircleIcon = () => (
    <Svg
        style={styles.resultDetailIcon}
        viewBox="0 0 24 24"
        fill="none"
        stroke="#ef4444"
    >
        <Path
            strokeLinecap="round"
            strokeLinejoin="round"
            strokeWidth="2"
            d="M10 14l2-2m0 0l2-2m-2 2l-2-2m2 2l2 2m7-2a9 9 0 11-18 0 9 9 0 0118 0z"
        />
    </Svg>
);
const UploadIcon = () => (
    <Svg
        style={styles.uploadIcon}
        stroke="currentColor"
        fill="none"
        viewBox="0 0 48 48"
    >
        <Path
            d="M28 8H12a4 4 0 00-4 4v20m32-12v8m0 0v8a4 4 0 01-4 4H12a4 4 0 01-4-4v-4m32-4l-3.172-3.172a4 4 0 00-5.656 0L28 28M8 32l9.172-9.172a4 4 0 015.656 0L28 28m0 0l4 4m4-24h8m-4-4v8"
            strokeWidth="2"
            strokeLinecap="round"
            strokeLinejoin="round"
        />
    </Svg>
);
const CloseIcon = () => (
    <Svg
        style={{ width: 24, height: 24 }}
        fill="none"
        stroke="currentColor"
        viewBox="0 0 24 24"
    >
        <Path
            strokeLinecap="round"
            strokeLinejoin="round"
            strokeWidth="2"
            d="M6 18L18 6M6 6l12 12"
        ></Path>
    </Svg>
);
const SuccessIcon = () => (
    <Svg
        style={{ height: 40, width: 40 }}
        fill="none"
        stroke="#16a34a"
        viewBox="0 0 24 24"
    >
        <Path
            strokeLinecap="round"
            strokeLinejoin="round"
            strokeWidth="2"
            d="M5 13l4 4L19 7"
        ></Path>
    </Svg>
);
const ErrorIcon = () => (
    <Svg
        style={{ height: 40, width: 40 }}
        fill="none"
        stroke="#dc2626"
        viewBox="0 0 24 24"
    >
        <Path
            strokeLinecap="round"
            strokeLinejoin="round"
            strokeWidth="2"
            d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"
        ></Path>
    </Svg>
);

export default function App() {
    // --- State Management ---
    const [modalVisible, setModalVisible] = useState(false);
    const [view, setView] = useState('upload'); // 'upload', 'progress', 'result'

    // Form inputs
    const [userName, setUserName] = useState('');
    const [panNumber, setPanNumber] = useState('');
    const [image, setImage] = useState(null);

    // Operation status
    const [progressText, setProgressText] = useState('');
    const [error, setError] = useState(null);
    const [resultData, setResultData] = useState(null);

    // --- Derived State ---
    const isFormComplete = useMemo(() => {
        return userName.trim() && panNumber.trim() && image;
    }, [userName, panNumber, image]);

    // --- Functions ---
    const resetState = () => {
        setView('upload');
        setUserName('');
        setPanNumber('');
        setImage(null);
        setProgressText('');
        setError(null);
        setResultData(null);
    };

    const handleOpenModal = () => {
        resetState();
        setModalVisible(true);
    };

    const handleCloseModal = () => {
        setModalVisible(false);
        // Delay reset to allow modal closing animation to finish
        setTimeout(resetState, 300);
    };

    const handleChoosePhoto = async () => {
        // Request permission to access the media library
        const permissionResult =
            await ImagePicker.requestMediaLibraryPermissionsAsync();
        if (permissionResult.granted === false) {
            alert("You've refused to allow this app to access your photos!");
            return;
        }

        // Launch the image picker
        let result = await ImagePicker.launchImageLibraryAsync({
            mediaTypes: ImagePicker.MediaTypeOptions.Images,
            allowsEditing: true,
            quality: 1,
            base64: true, // Important: ask for the base64 representation
        });

        if (!result.canceled) {
            setImage(result.assets[0]);
        }
    };

    const handleVerify = async () => {
        if (!isFormComplete) return;

        setView('progress');
        setError(null);
        setProgressText('Preparing document for analysis...');

        try {
            const prompt = `Analyze the document image. Extract the full name and the PAN number. Respond in a valid JSON format like this: {"name": "FULL_NAME", "pan": "PAN_NUMBER"}. If a field cannot be found, return null for its value.`;

            setProgressText('AI is extracting data from the document...');

            // Call API without the key, as it's now handled in the api file
            const aiData = await callGeminiAPI(
                image.base64,
                image.mimeType,
                prompt
            );

            const userInput = {
                name: userName.trim(),
                pan: panNumber.trim().toUpperCase(),
            };

            // Normalize AI data for comparison
            const aiName = aiData.name ? aiData.name.trim() : '';
            const aiPan = aiData.pan ? aiData.pan.trim().toUpperCase() : '';

            const nameMatch =
                aiName.toLowerCase() === userInput.name.toLowerCase();
            const panMatch = aiPan === userInput.pan;

            setResultData({
                name: { user: userInput.name, ai: aiName, match: nameMatch },
                pan: { user: userInput.pan, ai: aiPan, match: panMatch },
                overallSuccess: nameMatch && panMatch,
            });
        } catch (e) {
            setError(
                e.message || 'An unknown error occurred during verification.'
            );
        } finally {
            setView('result');
        }
    };

    // --- Render Helper Components ---
    const renderComparisonResult = (label, data) => (
        <View style={styles.resultDetailItem} key={label}>
            <View style={styles.resultDetailHeader}>
                <Text style={styles.resultDetailLabel}>{label}</Text>
                <View
                    style={[
                        styles.resultDetailBadge,
                        data.match ? styles.badgeSuccess : styles.badgeMismatch,
                    ]}
                >
                    {data.match ? <CheckCircleIcon /> : <XCircleIcon />}
                    <Text
                        style={[
                            styles.resultDetailBadgeText,
                            data.match
                                ? styles.textSuccess
                                : styles.textMismatch,
                        ]}
                    >
                        {data.match ? 'Matched' : 'Mismatch'}
                    </Text>
                </View>
            </View>
            <View style={styles.resultDetailBody}>
                <Text style={styles.resultDetailText}>
                    <Text style={{ fontWeight: '500' }}>Your Input:</Text>{' '}
                    {data.user}
                </Text>
                <Text style={styles.resultDetailText}>
                    <Text style={{ fontWeight: '500' }}>On Document:</Text>{' '}
                    {data.ai || 'Not Found'}
                </Text>
            </View>
        </View>
    );

    return (
        <SafeAreaView style={styles.screen}>
            <View style={styles.container}>
                <TouchableOpacity
                    style={styles.mainButton}
                    onPress={handleOpenModal}
                >
                    <Text style={styles.mainButtonText}>
                        AI KYC Verification
                    </Text>
                </TouchableOpacity>
            </View>

            <Modal
                animationType="slide"
                transparent={true}
                visible={modalVisible}
                onRequestClose={handleCloseModal}
            >
                <View style={styles.modalOverlay}>
                    <View style={styles.modalContent}>
                        <View style={styles.modalHeader}>
                            <View>
                                <Text style={styles.modalTitle}>
                                    AI Data Matching & Verification
                                </Text>
                                <Text style={styles.modalSubtitle}>
                                    Verify user data against an uploaded
                                    document.
                                </Text>
                            </View>
                            <TouchableOpacity onPress={handleCloseModal}>
                                <CloseIcon />
                            </TouchableOpacity>
                        </View>

                        <ScrollView
                            contentContainerStyle={styles.scrollViewContent}
                        >
                            {view === 'upload' && (
                                <View>
                                    <Text style={styles.label}>
                                        Full Name (as on card)
                                    </Text>
                                    <TextInput
                                        style={styles.input}
                                        placeholder="e.g., Rohan Varma"
                                        value={userName}
                                        onChangeText={setUserName}
                                    />

                                    <Text style={styles.label}>PAN Number</Text>
                                    <TextInput
                                        style={styles.input}
                                        placeholder="e.g., **********"
                                        value={panNumber}
                                        onChangeText={setPanNumber}
                                        autoCapitalize="characters"
                                    />

                                    <Text style={styles.label}>
                                        Upload PAN Card Image
                                    </Text>
                                    <TouchableOpacity
                                        style={styles.uploadBox}
                                        onPress={handleChoosePhoto}
                                    >
                                        <UploadIcon />
                                        <Text style={styles.uploadText}>
                                            Upload a file
                                        </Text>
                                        <Text style={styles.uploadSubtext}>
                                            PNG, JPG
                                        </Text>
                                        {image && (
                                            <Text style={styles.fileName}>
                                                Selected:{' '}
                                                {image.uri.split('/').pop()}
                                            </Text>
                                        )}
                                    </TouchableOpacity>

                                    <TouchableOpacity
                                        style={[
                                            styles.actionButton,
                                            !isFormComplete &&
                                                styles.disabledButton,
                                        ]}
                                        onPress={handleVerify}
                                        disabled={!isFormComplete}
                                    >
                                        <Text style={styles.actionButtonText}>
                                            Verify & Match Data
                                        </Text>
                                    </TouchableOpacity>
                                </View>
                            )}

                            {view === 'progress' && (
                                <View style={styles.centeredView}>
                                    <ActivityIndicator
                                        size="large"
                                        color="#14b8a6"
                                    />
                                    <Text style={styles.progressTitle}>
                                        AI Analysis in Progress...
                                    </Text>
                                    <Text style={styles.progressSubtitle}>
                                        {progressText}
                                    </Text>
                                </View>
                            )}

                            {view === 'result' && (
                                <View style={styles.centeredView}>
                                    {error ? (
                                        <>
                                            <View
                                                style={[
                                                    styles.resultIconContainer,
                                                    {
                                                        backgroundColor:
                                                            '#fee2e2',
                                                    },
                                                ]}
                                            >
                                                <ErrorIcon />
                                            </View>
                                            <Text style={styles.progressTitle}>
                                                Verification Error
                                            </Text>
                                            <Text
                                                style={styles.progressSubtitle}
                                            >
                                                {error}
                                            </Text>
                                        </>
                                    ) : (
                                        resultData && (
                                            <>
                                                <View
                                                    style={[
                                                        styles.resultIconContainer,
                                                        {
                                                            backgroundColor:
                                                                resultData.overallSuccess
                                                                    ? '#dcfce7'
                                                                    : '#fee2e2',
                                                        },
                                                    ]}
                                                >
                                                    {resultData.overallSuccess ? (
                                                        <SuccessIcon />
                                                    ) : (
                                                        <ErrorIcon />
                                                    )}
                                                </View>
                                                <Text
                                                    style={styles.progressTitle}
                                                >
                                                    {resultData.overallSuccess
                                                        ? 'Data Matched!'
                                                        : 'Data Mismatch'}
                                                </Text>
                                                <Text
                                                    style={
                                                        styles.progressSubtitle
                                                    }
                                                >
                                                    {resultData.overallSuccess
                                                        ? 'The details you provided match the data extracted from the document.'
                                                        : 'One or more fields did not match the document. Please review the details.'}
                                                </Text>
                                                <View
                                                    style={
                                                        styles.resultDetailsContainer
                                                    }
                                                >
                                                    <Text
                                                        style={
                                                            styles.resultDetailsTitle
                                                        }
                                                    >
                                                        Verification Details
                                                    </Text>
                                                    {renderComparisonResult(
                                                        'Full Name',
                                                        resultData.name
                                                    )}
                                                    {renderComparisonResult(
                                                        'PAN Number',
                                                        resultData.pan
                                                    )}
                                                </View>
                                            </>
                                        )
                                    )}
                                    <TouchableOpacity
                                        style={[
                                            styles.actionButton,
                                            { backgroundColor: '#4b5563' },
                                        ]}
                                        onPress={handleCloseModal}
                                    >
                                        <Text style={styles.actionButtonText}>
                                            Done
                                        </Text>
                                    </TouchableOpacity>
                                </View>
                            )}
                        </ScrollView>
                    </View>
                </View>
            </Modal>
        </SafeAreaView>
    );
}

// --- Stylesheet ---
const { width, height } = Dimensions.get('window');

const styles = StyleSheet.create({
    screen: { flex: 1, backgroundColor: '#f3f4f6' },
    container: {
        flex: 1,
        alignItems: 'center',
        justifyContent: 'center',
        padding: 20,
    },
    mainButton: {
        backgroundColor: '#0d9488',
        paddingVertical: 16,
        paddingHorizontal: 32,
        borderRadius: 8,
        shadowColor: '#000',
        shadowOffset: { width: 0, height: 2 },
        shadowOpacity: 0.25,
        shadowRadius: 3.84,
        elevation: 5,
    },
    mainButtonText: { color: '#ffffff', fontSize: 18, fontWeight: 'bold' },
    modalOverlay: {
        flex: 1,
        backgroundColor: 'rgba(0,0,0,0.6)',
        justifyContent: 'center',
        alignItems: 'center',
    },
    modalContent: {
        width: width * 0.9,
        maxHeight: height * 0.85,
        backgroundColor: 'white',
        borderRadius: 16,
        overflow: 'hidden',
        shadowColor: '#000',
        shadowOffset: { width: 0, height: 4 },
        shadowOpacity: 0.3,
        shadowRadius: 10,
        elevation: 10,
    },
    modalHeader: {
        flexDirection: 'row',
        justifyContent: 'space-between',
        alignItems: 'center',
        padding: 20,
        borderBottomWidth: 1,
        borderBottomColor: '#e5e7eb',
    },
    modalTitle: { fontSize: 22, fontWeight: 'bold', color: '#1f2937' },
    modalSubtitle: { fontSize: 14, color: '#6b7280', marginTop: 4 },
    scrollViewContent: { padding: 20 },
    label: {
        fontSize: 14,
        fontWeight: '500',
        color: '#374151',
        marginBottom: 8,
        marginTop: 16,
    },
    input: {
        backgroundColor: '#f9fafb',
        borderWidth: 1,
        borderColor: '#d1d5db',
        borderRadius: 8,
        padding: 12,
        fontSize: 16,
    },
    uploadBox: {
        borderWidth: 2,
        borderColor: '#d1d5db',
        borderStyle: 'dashed',
        borderRadius: 8,
        padding: 20,
        alignItems: 'center',
        justifyContent: 'center',
        backgroundColor: '#f9fafb',
    },
    uploadIcon: { height: 48, width: 48, color: '#9ca3af', marginBottom: 8 },
    uploadText: { color: '#0d9488', fontWeight: '500', fontSize: 16 },
    uploadSubtext: { color: '#6b7280', fontSize: 12, marginTop: 4 },
    fileName: {
        color: '#047857',
        fontSize: 14,
        fontWeight: '600',
        marginTop: 12,
    },
    actionButton: {
        backgroundColor: '#0d9488',
        padding: 16,
        borderRadius: 8,
        alignItems: 'center',
        marginTop: 24,
    },
    actionButtonText: { color: '#ffffff', fontSize: 16, fontWeight: 'bold' },
    disabledButton: { backgroundColor: '#9ca3af' },
    centeredView: {
        alignItems: 'center',
        justifyContent: 'center',
        paddingVertical: 40,
    },
    progressTitle: {
        fontSize: 20,
        fontWeight: '600',
        color: '#1f2937',
        marginTop: 16,
    },
    progressSubtitle: {
        fontSize: 16,
        color: '#6b7280',
        marginTop: 8,
        textAlign: 'center',
        paddingHorizontal: 20,
    },
    resultIconContainer: {
        width: 64,
        height: 64,
        borderRadius: 32,
        alignItems: 'center',
        justifyContent: 'center',
        marginBottom: 16,
    },
    resultDetailsContainer: {
        marginTop: 24,
        borderWidth: 1,
        borderColor: '#e5e7eb',
        backgroundColor: '#f9fafb',
        borderRadius: 8,
        padding: 16,
        width: '100%',
    },
    resultDetailsTitle: {
        fontSize: 18,
        fontWeight: 'bold',
        color: '#1f2937',
        marginBottom: 12,
    },
    resultDetailItem: {
        backgroundColor: 'white',
        borderRadius: 8,
        borderWidth: 1,
        borderColor: '#e5e7eb',
        padding: 12,
        marginBottom: 8,
    },
    resultDetailHeader: {
        flexDirection: 'row',
        justifyContent: 'space-between',
        alignItems: 'center',
    },
    resultDetailLabel: { fontSize: 16, fontWeight: '600', color: '#374151' },
    resultDetailBadge: {
        flexDirection: 'row',
        alignItems: 'center',
        paddingHorizontal: 8,
        paddingVertical: 4,
        borderRadius: 12,
    },
    badgeSuccess: { backgroundColor: '#dcfce7' },
    badgeMismatch: { backgroundColor: '#fee2e2' },
    resultDetailIcon: { width: 20, height: 20 },
    resultDetailBadgeText: { marginLeft: 6, fontWeight: 'bold' },
    textSuccess: { color: '#16a34a' },
    textMismatch: { color: '#b91c1c' },
    resultDetailBody: {
        marginTop: 8,
        paddingTop: 8,
        borderTopWidth: 1,
        borderColor: '#f3f4f6',
    },
    resultDetailText: { fontSize: 14, color: '#4b5563', lineHeight: 20 },
});
