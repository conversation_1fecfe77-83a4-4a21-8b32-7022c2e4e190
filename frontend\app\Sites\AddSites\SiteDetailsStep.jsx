import React from 'react';
import {
    View,
    Text,
    TextInput,
    TouchableOpacity,
    Alert,
    ScrollView,
} from 'react-native';
import { LinearGradient } from 'expo-linear-gradient';
import { Ionicons } from '@expo/vector-icons';
import * as DocumentPicker from 'expo-document-picker';
import { styles } from '../siteStyles';
import {
    FILE_CONSTRAINTS,
    validateStepData,
} from '../validations/siteValidations';

const SiteDetailsStep = ({
    theme,
    formik,
    setStep,
    isSubmitting,
    onDocumentPreview,
}) => {
    const { values, errors, touched, setFieldValue, handleChange, handleBlur } =
        formik;

    const pickSiteImages = async () => {
        try {
            const res = await DocumentPicker.getDocumentAsync({
                type: FILE_CONSTRAINTS.ALLOWED_TYPES,
                multiple: true,
            });
            if (res.canceled) return;
            const validAssets = [];
            for (const asset of res.assets) {
                if (asset.size > FILE_CONSTRAINTS.MAX_FILE_SIZE) {
                    Alert.alert(
                        'File too large',
                        `${asset.name} is too large. Max 5 MB allowed.`
                    );
                    continue;
                }
                if (!FILE_CONSTRAINTS.ALLOWED_TYPES.includes(asset.mimeType)) {
                    Alert.alert(
                        'Invalid type',
                        `${asset.name} is not a valid file type. Choose JPG, PNG, or PDF`
                    );
                    continue;
                }
                validAssets.push(asset);
            }
            if (
                values.siteImages.length + validAssets.length >
                FILE_CONSTRAINTS.MAX_SITE_IMAGES
            ) {
                Alert.alert(
                    'Limit reached',
                    `Maximum ${FILE_CONSTRAINTS.MAX_SITE_IMAGES} files allowed.`
                );
                return;
            }
            setFieldValue('siteImages', [...values.siteImages, ...validAssets]);
        } catch (error) {
            Alert.alert('Error', 'Failed to select files');
        }
    };

    const removeImage = (index) => {
        const updatedImages = values.siteImages.filter((_, i) => i !== index);
        setFieldValue('siteImages', updatedImages);
    };

    const previewImage = (image) => {
        if (image.uri && onDocumentPreview) {
            onDocumentPreview(image.uri, 'Site Image');
        }
    };

    const handleNext = async () => {
        const stepValidation = await validateStepData('siteDetails', values);
        if (stepValidation.isValid) {
            setStep('location');
        } else {
            // Show first error
            const firstError = Object.values(stepValidation.errors)[0];
            if (firstError) {
                Alert.alert('Validation Error', firstError);
            }
        }
    };

    return (
        <>
            <Text style={[styles.sectionTitle, { color: theme.PRIMARY }]}>
                Basic Details
            </Text>
            {/* Site Name */}
            <View
                style={[
                    styles.inputContainer,
                    {
                        backgroundColor: theme.INPUT_BACKGROUND,
                        borderColor: theme.INPUT_BORDER,
                    },
                ]}
            >
                <Ionicons
                    name="business-outline"
                    size={22}
                    color={theme.PRIMARY}
                    style={styles.inputIcon}
                />
                <TextInput
                    placeholder="Site name*"
                    value={values.name}
                    onChangeText={handleChange('name')}
                    onBlur={handleBlur('name')}
                    style={[styles.input, { color: theme.TEXT_PRIMARY }]}
                    placeholderTextColor={theme.TEXT_SECONDARY}
                />
            </View>
            {errors.name && touched.name && (
                <Text style={[styles.errorText, { color: theme.ERROR }]}>
                    {errors.name}
                </Text>
            )}
            {/* Address Line 1 */}
            <View
                style={[
                    styles.inputContainer,
                    {
                        backgroundColor: theme.INPUT_BACKGROUND,
                        borderColor: theme.INPUT_BORDER,
                    },
                ]}
            >
                <Ionicons
                    name="location-outline"
                    size={22}
                    color={theme.PRIMARY}
                    style={styles.inputIcon}
                />
                <TextInput
                    placeholder="Address line 1*"
                    value={values.addressLine1}
                    onChangeText={handleChange('addressLine1')}
                    onBlur={handleBlur('addressLine1')}
                    style={[styles.input, { color: theme.TEXT_PRIMARY }]}
                    placeholderTextColor={theme.TEXT_SECONDARY}
                />
            </View>
            {errors.addressLine1 && touched.addressLine1 && (
                <Text style={[styles.errorText, { color: theme.ERROR }]}>
                    {errors.addressLine1}
                </Text>
            )}

            {/* Address Line 2 */}
            <View
                style={[
                    styles.inputContainer,
                    {
                        backgroundColor: theme.INPUT_BACKGROUND,
                        borderColor: theme.INPUT_BORDER,
                    },
                ]}
            >
                <Ionicons
                    name="location-outline"
                    size={22}
                    color={theme.PRIMARY}
                    style={styles.inputIcon}
                />
                <TextInput
                    placeholder="Address line 2"
                    value={values.addressLine2}
                    onChangeText={handleChange('addressLine2')}
                    onBlur={handleBlur('addressLine2')}
                    style={[styles.input, { color: theme.TEXT_PRIMARY }]}
                    placeholderTextColor={theme.TEXT_SECONDARY}
                />
            </View>

            {/* Landmark */}
            <View
                style={[
                    styles.inputContainer,
                    {
                        backgroundColor: theme.INPUT_BACKGROUND,
                        borderColor: theme.INPUT_BORDER,
                    },
                ]}
            >
                <Ionicons
                    name="flag-outline"
                    size={22}
                    color={theme.PRIMARY}
                    style={styles.inputIcon}
                />
                <TextInput
                    placeholder="Landmark"
                    value={values.landmark}
                    onChangeText={handleChange('landmark')}
                    onBlur={handleBlur('landmark')}
                    style={[styles.input, { color: theme.TEXT_PRIMARY }]}
                    placeholderTextColor={theme.TEXT_SECONDARY}
                />
            </View>

            {/* Pincode */}
            <View
                style={[
                    styles.inputContainer,
                    {
                        backgroundColor: theme.INPUT_BACKGROUND,
                        borderColor: theme.INPUT_BORDER,
                    },
                ]}
            >
                <Ionicons
                    name="mail-outline"
                    size={22}
                    color={theme.PRIMARY}
                    style={styles.inputIcon}
                />
                <TextInput
                    placeholder="Pincode*"
                    value={values.pincode}
                    onChangeText={handleChange('pincode')}
                    onBlur={handleBlur('pincode')}
                    keyboardType="numeric"
                    maxLength={6}
                    style={[styles.input, { color: theme.TEXT_PRIMARY }]}
                    placeholderTextColor={theme.TEXT_SECONDARY}
                />
            </View>
            {errors.pincode && touched.pincode && (
                <Text style={[styles.errorText, { color: theme.ERROR }]}>
                    {errors.pincode}
                </Text>
            )}

            {/* State */}
            <View
                style={[
                    styles.inputContainer,
                    {
                        backgroundColor: theme.INPUT_BACKGROUND,
                        borderColor: theme.INPUT_BORDER,
                    },
                ]}
            >
                <Ionicons
                    name="map-outline"
                    size={22}
                    color={theme.PRIMARY}
                    style={styles.inputIcon}
                />
                <TextInput
                    placeholder="State*"
                    value={values.state}
                    onChangeText={handleChange('state')}
                    onBlur={handleBlur('state')}
                    style={[styles.input, { color: theme.TEXT_PRIMARY }]}
                    placeholderTextColor={theme.TEXT_SECONDARY}
                />
            </View>
            {errors.state && touched.state && (
                <Text style={[styles.errorText, { color: theme.ERROR }]}>
                    {errors.state}
                </Text>
            )}

            {/* District */}
            <View
                style={[
                    styles.inputContainer,
                    {
                        backgroundColor: theme.INPUT_BACKGROUND,
                        borderColor: theme.INPUT_BORDER,
                    },
                ]}
            >
                <Ionicons
                    name="map-outline"
                    size={22}
                    color={theme.PRIMARY}
                    style={styles.inputIcon}
                />
                <TextInput
                    placeholder="District*"
                    value={values.district}
                    onChangeText={handleChange('district')}
                    onBlur={handleBlur('district')}
                    style={[styles.input, { color: theme.TEXT_PRIMARY }]}
                    placeholderTextColor={theme.TEXT_SECONDARY}
                />
            </View>
            {errors.district && touched.district && (
                <Text style={[styles.errorText, { color: theme.ERROR }]}>
                    {errors.district}
                </Text>
            )}

            {/* Plot Area */}
            <View
                style={[
                    styles.inputContainer,
                    {
                        backgroundColor: theme.INPUT_BACKGROUND,
                        borderColor: theme.INPUT_BORDER,
                    },
                ]}
            >
                <Ionicons
                    name="resize-outline"
                    size={22}
                    color={theme.PRIMARY}
                    style={styles.inputIcon}
                />
                <TextInput
                    placeholder="Plot area (sq ft)*"
                    value={values.plotArea}
                    onChangeText={handleChange('plotArea')}
                    onBlur={handleBlur('plotArea')}
                    keyboardType="numeric"
                    style={[styles.input, { color: theme.TEXT_PRIMARY }]}
                    placeholderTextColor={theme.TEXT_SECONDARY}
                />
            </View>
            {errors.plotArea && touched.plotArea && (
                <Text style={[styles.errorText, { color: theme.ERROR }]}>
                    {errors.plotArea}
                </Text>
            )}

            {/* Price */}
            <View
                style={[
                    styles.inputContainer,
                    {
                        backgroundColor: theme.INPUT_BACKGROUND,
                        borderColor: theme.INPUT_BORDER,
                    },
                ]}
            >
                <Ionicons
                    name="wallet-outline"
                    size={22}
                    color={theme.PRIMARY}
                    style={styles.inputIcon}
                />
                <TextInput
                    placeholder="Price (₹)*"
                    value={values.price}
                    onChangeText={handleChange('price')}
                    onBlur={handleBlur('price')}
                    keyboardType="numeric"
                    style={[styles.input, { color: theme.TEXT_PRIMARY }]}
                    placeholderTextColor={theme.TEXT_SECONDARY}
                />
            </View>
            {errors.price && touched.price && (
                <Text style={[styles.errorText, { color: theme.ERROR }]}>
                    {errors.price}
                </Text>
            )}

            {/* Site Images */}
            <Text style={[styles.sectionTitle, { color: theme.PRIMARY }]}>
                Site Images
            </Text>
            <TouchableOpacity
                style={[
                    styles.filePickerButton,
                    {
                        backgroundColor:
                            values.siteImages.length > 0
                                ? theme.ACCENT
                                : theme.INPUT_BACKGROUND,
                        borderColor:
                            values.siteImages.length > 0
                                ? theme.PRIMARY
                                : theme.INPUT_BORDER,
                    },
                ]}
                onPress={pickSiteImages}
            >
                <Ionicons
                    name={
                        values.siteImages.length > 0
                            ? 'checkmark-circle'
                            : 'cloud-upload-outline'
                    }
                    size={22}
                    color={
                        values.siteImages.length > 0
                            ? theme.PRIMARY
                            : theme.TEXT_SECONDARY
                    }
                    style={styles.inputIcon}
                />
                <Text
                    style={[
                        styles.filePickerText,
                        {
                            color:
                                values.siteImages.length > 0
                                    ? theme.PRIMARY
                                    : theme.TEXT_SECONDARY,
                        },
                    ]}
                >
                    {values.siteImages.length > 0
                        ? `${values.siteImages.length} image${values.siteImages.length > 1 ? 's' : ''} selected`
                        : `Pick site images (JPG/PNG/PDF, max ${FILE_CONSTRAINTS.MAX_SITE_IMAGES})`}
                </Text>
            </TouchableOpacity>
            {errors.siteImages && touched.siteImages && (
                <Text style={[styles.errorText, { color: theme.ERROR }]}>
                    {errors.siteImages}
                </Text>
            )}
            {/* Display selected images */}
            {values.siteImages.length > 0 && (
                <ScrollView
                    horizontal
                    showsHorizontalScrollIndicator={false}
                    style={styles.imageGrid}
                >
                    {values.siteImages.map((image, index) => (
                        <View
                            key={index}
                            style={[
                                styles.selectedFile,
                                {
                                    backgroundColor: theme.ACCENT,
                                    borderColor: theme.INPUT_BORDER,
                                },
                            ]}
                        >
                            <Ionicons
                                name="document-outline"
                                size={16}
                                color={theme.PRIMARY}
                            />
                            <Text
                                style={[
                                    styles.selectedFileName,
                                    { color: theme.TEXT_PRIMARY },
                                ]}
                                numberOfLines={1}
                            >
                                {image.name}
                            </Text>
                            <TouchableOpacity
                                onPress={() => previewImage(image)}
                            >
                                <Ionicons
                                    name="eye-outline"
                                    size={20}
                                    color={theme.GRAY}
                                />
                            </TouchableOpacity>
                            <TouchableOpacity
                                style={styles.removeFileButton}
                                onPress={() => removeImage(index)}
                            >
                                <Ionicons
                                    name="close-circle"
                                    size={20}
                                    color={theme.ERROR}
                                />
                            </TouchableOpacity>
                        </View>
                    ))}
                </ScrollView>
            )}

            {/* Next Button */}
            <View style={styles.buttonContainer}>
                <TouchableOpacity
                    style={[styles.nextButton, { shadowColor: theme.PRIMARY }]}
                    onPress={handleNext}
                    activeOpacity={0.8}
                    disabled={isSubmitting}
                >
                    <LinearGradient
                        colors={[theme.PRIMARY, theme.SECONDARY]}
                        start={{ x: 0, y: 0 }}
                        end={{ x: 1, y: 0 }}
                        style={styles.nextButtonGradient}
                    >
                        <Text
                            style={[
                                styles.nextButtonText,
                                { color: theme.WHITE },
                            ]}
                        >
                            Next
                        </Text>
                    </LinearGradient>
                </TouchableOpacity>
            </View>
        </>
    );
};

export default SiteDetailsStep;
