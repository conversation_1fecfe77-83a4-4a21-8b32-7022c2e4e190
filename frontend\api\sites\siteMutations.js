import { useMutation, useQueryClient } from '@tanstack/react-query';
import { privateAPIClient } from '../index';
import { showToast } from '../../utils/showToast';

// Create Site Mutation
export const useCreateSite = () => {
    const queryClient = useQueryClient();

    return useMutation({
        mutationFn: async (formData) => {
            const response = await privateAPIClient.post(
                '/site-service/api/v1/sites',
                formData,
                {
                    headers: {
                        'Content-Type': 'multipart/form-data',
                    },
                }
            );
            return response.data;
        },
        onSuccess: (data) => {
            // Invalidate and refetch sites list
            queryClient.invalidateQueries({ queryKey: ['sites'] });
            queryClient.invalidateQueries({ queryKey: ['userSites'] });

            showToast(
                'success',
                'Site Created',
                'Your site has been successfully submitted for verification.'
            );
        },
        onError: (error) => {
            console.error('Site creation error:', error);
            const errorMessage =
                error.response?.data?.message ||
                error.message ||
                'Failed to create site listing';

            showToast('error', 'Creation Failed', errorMessage);
        },
    });
};

// Update Site Mutation
export const useUpdateSite = (siteId) => {
    const queryClient = useQueryClient();

    return useMutation({
        mutationFn: async (formData) => {
            const response = await privateAPIClient.put(
                `/site-service/api/v1/sites/${siteId}`,
                formData,
                {
                    headers: {
                        'Content-Type': 'multipart/form-data',
                    },
                }
            );
            return response.data;
        },
        onSuccess: (data) => {
            // Invalidate and refetch relevant queries
            queryClient.invalidateQueries({ queryKey: ['sites'] });
            queryClient.invalidateQueries({ queryKey: ['userSites'] });
            queryClient.invalidateQueries({ queryKey: ['site', siteId] });

            showToast(
                'success',
                'Site Updated',
                'Your site has been successfully updated.'
            );
        },
        onError: (error) => {
            console.error('Site update error:', error);
            const errorMessage =
                error.response?.data?.message ||
                error.message ||
                'Failed to update site listing';

            showToast('error', 'Update Failed', errorMessage);
        },
    });
};

// Delete Site Mutation
export const useDeleteSite = () => {
    const queryClient = useQueryClient();

    return useMutation({
        mutationFn: async (siteId) => {
            const response = await privateAPIClient.delete(
                `/site-service/api/v1/sites/${siteId}`
            );
            return response.data;
        },
        onSuccess: (data, siteId) => {
            // Remove from cache and invalidate queries
            queryClient.removeQueries({ queryKey: ['site', siteId] });
            queryClient.invalidateQueries({ queryKey: ['sites'] });
            queryClient.invalidateQueries({ queryKey: ['userSites'] });

            showToast(
                'success',
                'Site Deleted',
                'Your site listing has been successfully deleted.'
            );
        },
        onError: (error) => {
            console.error('Site deletion error:', error);
            const errorMessage =
                error.response?.data?.message ||
                error.message ||
                'Failed to delete site listing';

            showToast('error', 'Deletion Failed', errorMessage);
        },
    });
};

// Helper function to build FormData for site submission
export const buildSiteFormData = (values) => {
    const formData = new FormData();

    // Helper function to build file part
    const buildFilePart = (file) => ({
        uri: file.uri,
        name: file.name || `file_${Date.now()}`,
        type: file.mimeType || 'application/octet-stream',
    });

    // Add site images (only new files, not existing URLs)
    if (values.siteImages?.length) {
        values.siteImages.forEach((image) => {
            if (image.uri && !image.uri.startsWith('http')) {
                formData.append('siteImages', buildFilePart(image));
            }
        });
    }

    // Add encumbrance certificate (only new file, not existing URL)
    if (
        values.encumbranceCert?.uri &&
        !values.encumbranceCert.uri.startsWith('http')
    ) {
        formData.append(
            'encumbranceCertificate',
            buildFilePart(values.encumbranceCert)
        );
    }

    // Add property tax receipt (only new file, not existing URL)
    if (
        values.propertyTaxRec?.uri &&
        !values.propertyTaxRec.uri.startsWith('http')
    ) {
        formData.append(
            'propertyTaxReceipt',
            buildFilePart(values.propertyTaxRec)
        );
    }

    // Add other form fields (excluding file fields and metadata)
    const excludeFields = [
        'siteImages',
        'encumbranceCert',
        'propertyTaxRec',
        '_id',
        '__v',
        'createdAt',
        'updatedAt',
        'status',
        'verificationStatus',
    ];

    Object.keys(values).forEach((key) => {
        if (!excludeFields.includes(key)) {
            const value = values[key];
            if (value !== null && value !== undefined && value !== '') {
                formData.append(key, value);
            }
        }
    });

    return formData;
};

// Helper function to prepare values for update (handles existing vs new files)
export const prepareUpdateValues = (values) => {
    const updateValues = { ...values };

    // Handle site images - keep track of existing vs new
    if (updateValues.siteImages?.length) {
        updateValues.siteImages = updateValues.siteImages.filter((image) => {
            // Keep new files (with local uri) and existing files (with http uri)
            return (
                image.uri &&
                (image.uri.startsWith('http') || !image.uri.startsWith('http'))
            );
        });
    }

    // Handle documents - preserve existing if no new file selected
    if (updateValues.encumbranceCert?.uri?.startsWith('http')) {
        // Keep existing document reference
        updateValues.existingEncumbranceCert = updateValues.encumbranceCert.uri;
    }

    if (updateValues.propertyTaxRec?.uri?.startsWith('http')) {
        // Keep existing document reference
        updateValues.existingPropertyTaxRec = updateValues.propertyTaxRec.uri;
    }

    return updateValues;
};
