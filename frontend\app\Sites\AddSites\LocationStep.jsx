import React from 'react';
import { View, Text, TextInput, TouchableOpacity, Alert } from 'react-native';
import { LinearGradient } from 'expo-linear-gradient';
import { Ionicons } from '@expo/vector-icons';
import { styles } from '../siteStyles';
import { validateStepData } from '../validations/siteValidations';

const LocationStep = ({ theme, formik, setStep, isSubmitting }) => {
    const { values, errors, touched, handleChange, handleBlur } = formik;

    const handleNext = async () => {
        const stepValidation = await validateStepData('location', values);
        if (stepValidation.isValid) {
            setStep('encumbrance');
        } else {
            // Show first error
            const firstError = Object.values(stepValidation.errors)[0];
            if (firstError) {
                Alert.alert('Validation Error', firstError);
            }
        }
    };

    const handleBack = () => {
        setStep('siteDetails');
    };

    return (
        <>
            <Text style={[styles.sectionTitle, { color: theme.PRIMARY }]}>
                Location Details
            </Text>

            {/* Location */}
            <View
                style={[
                    styles.inputContainer,
                    {
                        backgroundColor: theme.INPUT_BACKGROUND,
                        borderColor: theme.INPUT_BORDER,
                    },
                ]}
            >
                <Ionicons
                    name="location-outline"
                    size={22}
                    color={theme.PRIMARY}
                    style={styles.inputIcon}
                />
                <TextInput
                    placeholder="Location description*"
                    value={values.location}
                    onChangeText={handleChange('location')}
                    onBlur={handleBlur('location')}
                    style={[styles.input, { color: theme.TEXT_PRIMARY }]}
                    placeholderTextColor={theme.TEXT_SECONDARY}
                />
            </View>
            {errors.location && touched.location && (
                <Text style={[styles.errorText, { color: theme.ERROR }]}>
                    {errors.location}
                </Text>
            )}

            {/* Latitude */}
            <View
                style={[
                    styles.inputContainer,
                    {
                        backgroundColor: theme.INPUT_BACKGROUND,
                        borderColor: theme.INPUT_BORDER,
                    },
                ]}
            >
                <Ionicons
                    name="navigate-outline"
                    size={22}
                    color={theme.PRIMARY}
                    style={styles.inputIcon}
                />
                <TextInput
                    placeholder="Latitude*"
                    value={values.latitude}
                    onChangeText={handleChange('latitude')}
                    onBlur={handleBlur('latitude')}
                    keyboardType="numeric"
                    style={[styles.input, { color: theme.TEXT_PRIMARY }]}
                    placeholderTextColor={theme.TEXT_SECONDARY}
                />
            </View>
            {errors.latitude && touched.latitude && (
                <Text style={[styles.errorText, { color: theme.ERROR }]}>
                    {errors.latitude}
                </Text>
            )}

            {/* Longitude */}
            <View
                style={[
                    styles.inputContainer,
                    {
                        backgroundColor: theme.INPUT_BACKGROUND,
                        borderColor: theme.INPUT_BORDER,
                    },
                ]}
            >
                <Ionicons
                    name="navigate-outline"
                    size={22}
                    color={theme.PRIMARY}
                    style={styles.inputIcon}
                />
                <TextInput
                    placeholder="Longitude*"
                    value={values.longitude}
                    onChangeText={handleChange('longitude')}
                    onBlur={handleBlur('longitude')}
                    keyboardType="numeric"
                    style={[styles.input, { color: theme.TEXT_PRIMARY }]}
                    placeholderTextColor={theme.TEXT_SECONDARY}
                />
            </View>
            {errors.longitude && touched.longitude && (
                <Text style={[styles.errorText, { color: theme.ERROR }]}>
                    {errors.longitude}
                </Text>
            )}

            {/* Village */}
            <View
                style={[
                    styles.inputContainer,
                    {
                        backgroundColor: theme.INPUT_BACKGROUND,
                        borderColor: theme.INPUT_BORDER,
                    },
                ]}
            >
                <Ionicons
                    name="home-outline"
                    size={22}
                    color={theme.PRIMARY}
                    style={styles.inputIcon}
                />
                <TextInput
                    placeholder="Village*"
                    value={values.village}
                    onChangeText={handleChange('village')}
                    onBlur={handleBlur('village')}
                    style={[styles.input, { color: theme.TEXT_PRIMARY }]}
                    placeholderTextColor={theme.TEXT_SECONDARY}
                />
            </View>
            {errors.village && touched.village && (
                <Text style={[styles.errorText, { color: theme.ERROR }]}>
                    {errors.village}
                </Text>
            )}

            {/* Survey Number */}
            <View
                style={[
                    styles.inputContainer,
                    {
                        backgroundColor: theme.INPUT_BACKGROUND,
                        borderColor: theme.INPUT_BORDER,
                    },
                ]}
            >
                <Ionicons
                    name="document-text-outline"
                    size={22}
                    color={theme.PRIMARY}
                    style={styles.inputIcon}
                />
                <TextInput
                    placeholder="Survey Number*"
                    value={values.surveyNumber}
                    onChangeText={handleChange('surveyNumber')}
                    onBlur={handleBlur('surveyNumber')}
                    style={[styles.input, { color: theme.TEXT_PRIMARY }]}
                    placeholderTextColor={theme.TEXT_SECONDARY}
                />
            </View>
            {errors.surveyNumber && touched.surveyNumber && (
                <Text style={[styles.errorText, { color: theme.ERROR }]}>
                    {errors.surveyNumber}
                </Text>
            )}

            {/* Navigation Buttons */}
            <View style={styles.buttonContainer}>
                <TouchableOpacity
                    style={[
                        styles.backButton,
                        { backgroundColor: theme.GRAY_LIGHT },
                    ]}
                    onPress={handleBack}
                    activeOpacity={0.8}
                >
                    <Text
                        style={[
                            styles.backButtonText,
                            { color: theme.TEXT_PRIMARY },
                        ]}
                    >
                        Back
                    </Text>
                </TouchableOpacity>
                <TouchableOpacity
                    style={[styles.nextButton, { shadowColor: theme.PRIMARY }]}
                    onPress={handleNext}
                    activeOpacity={0.8}
                >
                    <LinearGradient
                        colors={[theme.PRIMARY, theme.SECONDARY]}
                        start={{ x: 0, y: 0 }}
                        end={{ x: 1, y: 0 }}
                        style={styles.nextButtonGradient}
                    >
                        <Text
                            style={[
                                styles.nextButtonText,
                                { color: theme.WHITE },
                            ]}
                        >
                            Next
                        </Text>
                    </LinearGradient>
                </TouchableOpacity>
            </View>
        </>
    );
};

export default LocationStep;
