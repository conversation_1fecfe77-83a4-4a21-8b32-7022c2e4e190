import * as Yup from 'yup';

// Common regex patterns used across the application
export const REGEX_PATTERNS = {
    NAME: /^[a-zA-Z\s.]+$/, // Only alphabets, spaces, and dots
    AADHAAR: /^\d{12}$/, // Exactly 12 digits
    PAN: /^[A-Z]{3}[ABCFGHLJPT][A-Z][0-9]{4}[A-Z]$/, // PAN format
    COORDINATE: /^-?\d+(\.\d+)?$/, // Validates latitude/longitude
    PINCODE: /^\d{6}$/, // Exactly 6 digits
    ENCUMBRANCE_DOC: /^\d{1,6}\/\d{4}$/, // Format: docNo/year
    PROPERTY_TAX:
        /^([A-Z]{0,3}\d{1,3}|[A-Z]{2,5})[/-]\d{1,4}[/-]\d{4}(?:-[0-9]{2})?[/-]\d{3,6}$/i,
    SURVEY_NO: /^\d+[A-Z]?(?:\/\d+[A-Z]?)?$/i, // 123, 123/1, or 123/1A
    VILLAGE_DISTRICT: /^[A-Za-z\s.'-]+$/, // Letters, spaces, apostrophes, periods, hyphens
};

// Common validation helpers
export const createDateValidation = (fieldName = 'Date') =>
    Yup.date()
        .transform((value, originalValue) => {
            if (!originalValue) return null;
            const parsed = new Date(originalValue);
            return isNaN(parsed) ? null : parsed;
        })
        .required(`${fieldName} is required`)
        .test('age-range', 'Age must be between 18 and 100 years', (value) => {
            if (!value) return false;
            const birthDate = new Date(value);
            const today = new Date();
            const age = today.getFullYear() - birthDate.getFullYear();
            return age >= 18 && age <= 100;
        });

export const createNameValidation = (fieldName, required = true) => {
    const validation = Yup.string()
        .trim()
        .matches(
            REGEX_PATTERNS.NAME,
            `${fieldName} can only contain letters, spaces, and dots`
        )
        .min(2, `${fieldName} must be at least 2 characters`)
        .max(100, `${fieldName} must not exceed 100 characters`);

    return required
        ? validation.required(`${fieldName} is required`)
        : validation;
};

export const createFileValidation = (fieldName, required = true) => {
    const validation = Yup.object().shape({
        uri: Yup.string().required(),
        name: Yup.string().required(),
        mimeType: Yup.string().required(),
    });

    return required
        ? validation.required(`${fieldName} is required`)
        : validation;
};

// Broker/Contractor validation schema
export const validationSchema = Yup.object().shape({
    aadhaarNumber: Yup.string()
        .matches(
            REGEX_PATTERNS.AADHAAR,
            'Enter a valid 12-digit Aadhaar number'
        )
        .required('Aadhaar number is required'),
    nameOnAadhaar: createNameValidation('Name on Aadhaar'),
    dateOfBirth: createDateValidation('Date of birth'),
    gender: Yup.string()
        .oneOf(['Male', 'Female', 'Other'], 'Select a gender')
        .required('Gender is required'),
    address: Yup.string()
        .min(10, 'Address must be at least 10 characters')
        .max(500, 'Address must not exceed 500 characters')
        .required('Address is required'),
    panNumber: Yup.string()
        .matches(REGEX_PATTERNS.PAN, 'Enter a valid 10-character PAN number')
        .required('PAN number is required'),
    panName: createNameValidation('Name on PAN'),
    panDateOfBirth: createDateValidation('Date of birth'),
    experience: Yup.number()
        .typeError('Enter a valid non-negative number')
        .min(0, 'Experience must be a number between 0 and 50 years')
        .max(50, 'Experience must be a number between 0 and 50 years')
        .required('Experience is required'),
    serviceAreas: Yup.array()
        .of(
            Yup.string().max(
                100,
                'Each service area must be less than or equal to 100 characters'
            )
        )
        .min(1, 'Enter at least one service area')
        .max(10, 'Maximum 10 service areas allowed')
        .required('Service areas are required'),
    portfolio: Yup.array()
        .of(
            Yup.string().max(
                500,
                'Each portfolio item must be less than or equal to 500 characters'
            )
        )
        .max(10, 'Maximum 10 portfolio items allowed')
        .optional(),
    specialties: Yup.array()
        .of(
            Yup.string().max(
                100,
                'Each specialty must be less than or equal to 100 characters'
            )
        )
        .max(15, 'Maximum 15 specialties allowed')
        .optional(),
    aadhaarDocument: Yup.mixed().when(
        'contractorId',
        (contractorId, schema) => {
            return contractorId
                ? schema.optional()
                : schema.required('Aadhaar document is required');
        }
    ),
    panDocument: Yup.mixed().when('contractorId', (contractorId, schema) => {
        return contractorId
            ? schema.optional()
            : schema.required('PAN document is required');
    }),
});

// Site validation schemas
export const siteDetailsSchema = Yup.object().shape({
    name: Yup.string()
        .trim()
        .required('Site name is required')
        .min(5, 'Site name must be at least 5 characters')
        .max(100, 'Site name must not exceed 100 characters')
        .matches(
            REGEX_PATTERNS.NAME,
            'Site name must contain only letters and spaces'
        ),

    addressLine1: Yup.string()
        .trim()
        .required('Address Line 1 is required')
        .max(200, 'Address Line 1 is too long'),

    addressLine2: Yup.string().trim().max(200, 'Address Line 2 is too long'),

    landmark: Yup.string().trim().max(100, 'Landmark is too long'),

    pincode: Yup.string()
        .trim()
        .required('Pincode is required')
        .matches(REGEX_PATTERNS.PINCODE, 'Pincode must be exactly 6 digits'),

    city: Yup.string()
        .trim()
        .required('City is required')
        .max(100, 'City name is too long')
        .matches(REGEX_PATTERNS.VILLAGE_DISTRICT, 'Invalid city name'),

    state: Yup.string()
        .trim()
        .required('State is required')
        .max(100, 'State name is too long')
        .matches(REGEX_PATTERNS.VILLAGE_DISTRICT, 'Invalid state name'),

    area: Yup.number()
        .required('Area is required')
        .positive('Area must be a positive number')
        .min(1, 'Area must be at least 1 sq ft'),

    price: Yup.number()
        .required('Price is required')
        .positive('Price must be a positive number')
        .min(1, 'Price must be at least ₹1'),

    siteImages: Yup.array()
        .min(1, 'At least one site image is required')
        .max(10, 'Maximum 10 images allowed')
        .of(createFileValidation('Site image')),
});

export const locationSchema = Yup.object().shape({
    location: Yup.string()
        .trim()
        .required('Location is required')
        .max(200, 'Location is too long'),

    latitude: Yup.string()
        .required('Latitude is required')
        .matches(REGEX_PATTERNS.COORDINATE, 'Invalid latitude format')
        .test(
            'latitude-range',
            'Latitude must be between -90 and 90',
            function (value) {
                if (!value) return false;
                const lat = parseFloat(value);
                return lat >= -90 && lat <= 90;
            }
        ),

    longitude: Yup.string()
        .required('Longitude is required')
        .matches(REGEX_PATTERNS.COORDINATE, 'Invalid longitude format')
        .test(
            'longitude-range',
            'Longitude must be between -180 and 180',
            function (value) {
                if (!value) return false;
                const lng = parseFloat(value);
                return lng >= -180 && lng <= 180;
            }
        ),
});

export const encumbranceSchema = Yup.object().shape({
    encOwnerName: createNameValidation('Owner name'),

    encDocumentNo: Yup.string()
        .trim()
        .required('Encumbrance Certificate number is required')
        .matches(
            REGEX_PATTERNS.ENCUMBRANCE_DOC,
            'Format must be docNo/year (e.g., 1234/2024)'
        )
        .test('valid-year', 'Year in EC number is invalid', function (value) {
            if (!value) return false;
            const [, yearPart] = value.split('/');
            const year = Number(yearPart);
            return year >= 1900 && year <= new Date().getFullYear();
        }),

    encSurveyNo: Yup.string()
        .trim()
        .required('Survey number is required')
        .matches(
            REGEX_PATTERNS.SURVEY_NO,
            'Invalid survey number format (e.g., 123, 123/1, 123/1A)'
        ),

    encVillage: Yup.string()
        .trim()
        .required('Village is required')
        .max(100, 'Village name is too long')
        .matches(REGEX_PATTERNS.VILLAGE_DISTRICT, 'Invalid village name'),

    encDistrict: Yup.string()
        .trim()
        .required('District is required')
        .max(100, 'District name is too long')
        .matches(REGEX_PATTERNS.VILLAGE_DISTRICT, 'Invalid district name'),

    encumbranceCert: createFileValidation('Encumbrance Certificate'),
});

export const propertyTaxSchema = Yup.object().shape({
    ptrOwnerName: createNameValidation('Owner name'),

    ptrReciptNo: Yup.string()
        .trim()
        .required('Tax receipt number is required')
        .min(6, 'Tax receipt number must be at least 6 characters')
        .max(30, 'Tax receipt number must not exceed 30 characters')
        .matches(
            REGEX_PATTERNS.PROPERTY_TAX,
            'Invalid tax receipt format (e.g., ZN/013/2024-25/00123)'
        ),

    propertyTaxRec: createFileValidation('Property Tax Receipt'),
});

// Complete Site Validation Schema
export const completeSiteSchema = Yup.object().shape({
    ...siteDetailsSchema.fields,
    ...locationSchema.fields,
    ...encumbranceSchema.fields,
    ...propertyTaxSchema.fields,
});

// Helper function to get step-specific validation schema
export const getStepValidationSchema = (step) => {
    switch (step) {
        case 'siteDetails':
            return siteDetailsSchema;
        case 'location':
            return locationSchema;
        case 'encumbrance':
            return encumbranceSchema;
        case 'propertyTax':
            return propertyTaxSchema;
        case 'review':
            return completeSiteSchema;
        default:
            return Yup.object();
    }
};

// Initial values for site form
export const initialSiteValues = {
    // Site Details
    name: '',
    addressLine1: '',
    addressLine2: '',
    landmark: '',
    pincode: '',
    city: '',
    state: '',
    area: '',
    price: '',
    siteImages: [],

    // Location
    location: '',
    latitude: '',
    longitude: '',

    // Encumbrance
    encOwnerName: '',
    encDocumentNo: '',
    encSurveyNo: '',
    encVillage: '',
    encDistrict: '',
    encumbranceCert: null,

    // Property Tax
    ptrOwnerName: '',
    ptrReciptNo: '',
    propertyTaxRec: null,
};
