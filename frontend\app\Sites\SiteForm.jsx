import React, {
    useState,
    useRef,
    useEffect,
    useContext,
    useCallback,
} from 'react';
import {
    View,
    Image,
    ActivityIndicator,
    ScrollView,
    Text,
    Platform,
    KeyboardAvoidingView,
    StatusBar,
} from 'react-native';
import { LinearGradient } from 'expo-linear-gradient';
import { useRouter, useLocalSearchParams } from 'expo-router';
import { useQuery, useMutation } from '@tanstack/react-query';
import queryClient from '../../api/queryClient';
import { useFormik } from 'formik';
import { ThemeContext } from '../../context/ThemeContext';
import { fetchUserProfile } from '../../api/user/userApi';
import { showToast } from '../../utils/showToast';
import BackButton from '../Components/Shared/BackButton';
import DocumentPreviewModal from '../Components/Profile/DocumentPreviewModal';
import SiteDetailsStep from './AddSites/SiteDetailsStep';
import LocationStep from './AddSites/LocationStep';
import EncumbranceStep from './AddSites/EncumbranceStep';
import PropertyTaxStep from './AddSites/PropertyTaxStep';
import ReviewStep from './AddSites/ReviewStep';
import { styles } from './siteStyles';
import {
    createSiteApplication,
    updateSiteProfile,
} from '../../api/sites/sitesApi';
import {
    initialSiteValues,
    getStepValidationSchema,
    completeSiteSchema,
} from './validations/siteValidations';

const SiteForm = () => {
    const { theme, isDarkMode } = useContext(ThemeContext);
    const router = useRouter();
    const { editData } = useLocalSearchParams();
    const [step, setStep] = useState('siteDetails');
    const [showDocumentPreviewModal, setShowDocumentPreviewModal] =
        useState(false);
    const [selectedDocument, setSelectedDocument] = useState(null);
    const scrollViewRef = useRef(null);
    const [isSubmitting, setIsSubmitting] = useState(false);

    let parsedEditData = null;
    try {
        parsedEditData = editData ? JSON.parse(editData) : null;
    } catch (error) {
        showToast(
            'error',
            'Invalid Data',
            'Failed to load existing site data.'
        );
    }
    const isEditing = !!parsedEditData;
    const siteId = parsedEditData?.id;

    useEffect(() => {
        if (scrollViewRef.current) {
            scrollViewRef.current.scrollTo({ y: 0, animated: true });
        }
    }, [step]);

    const {
        data: user,
        isLoading,
        isError,
        error,
    } = useQuery({
        queryKey: ['userProfile'],
        queryFn: fetchUserProfile,
        refetchInterval: isEditing ? 30000 : false,
        refetchIntervalInBackground: false,
        enabled: true,
        onError: () => {
            showToast('error', 'Error', 'Failed to fetch user profile.');
        },
    });

    const { mutateAsync: updateSite, isLoading: isUpdating } = useMutation({
        mutationFn: ({ siteId, data }) => {
            return updateSiteProfile(siteId, data);
        },
        onSuccess: () => {
            setIsSubmitting(false);
            queryClient.invalidateQueries({
                queryKey: ['siteApplication', siteId],
            });
            showToast(
                'success',
                'Success',
                'Site listing updated successfully.'
            );
            router.replace('/Profile/Applications');
        },
        onError: (error) => {
            setIsSubmitting(false);
            if (error.response?.status === 409) {
                showToast('error', 'Error', 'Site listing already exists.');
            } else if (error.response?.status === 400) {
                showToast('error', 'Invalid Data', error.response.data.error);
            } else {
                showToast(
                    'error',
                    'Submission Error',
                    'Failed to update site listing.'
                );
            }
        },
    });

    const { mutateAsync: createSite, isLoading: isCreating } = useMutation({
        mutationFn: createSiteApplication,
        onSuccess: () => {
            setIsSubmitting(false);
            queryClient.invalidateQueries({ queryKey: ['siteApplication'] });
            router.replace('/Sites/SiteSuccess');
        },
        onError: (error) => {
            setIsSubmitting(false);
            if (error.response?.status === 409) {
                showToast('error', 'Error', 'You already have a site listing.');
            } else if (error.response?.status === 400) {
                showToast('error', 'Invalid Data', error.response.data.error);
            } else if (error.response?.status === 403) {
                showToast(
                    'error',
                    'Error',
                    'You need to complete your profile first.'
                );
            } else {
                showToast(
                    'error',
                    'Submission Error',
                    'Failed to submit site listing.'
                );
            }
        },
    });

    const formik = useFormik({
        initialValues: {
            // Site Details
            name: parsedEditData?.name || '',
            addressLine1: parsedEditData?.addressLine1 || '',
            addressLine2: parsedEditData?.addressLine2 || '',
            landmark: parsedEditData?.landmark || '',
            pincode: parsedEditData?.pincode || '',
            state: parsedEditData?.state || '',
            district: parsedEditData?.district || '',
            plotArea: parsedEditData?.plotArea || '',
            price: parsedEditData?.price || '',
            siteImages: parsedEditData?.siteImages || [],

            // Location Details
            location: parsedEditData?.location || '',
            latitude: parsedEditData?.latitude || '',
            longitude: parsedEditData?.longitude || '',
            village: parsedEditData?.village || '',
            surveyNumber: parsedEditData?.surveyNumber || '',

            // Encumbrance Details
            encumbranceDocNumber: parsedEditData?.encumbranceDocNumber || '',
            encumbranceDate: parsedEditData?.encumbranceDate
                ? new Date(parsedEditData.encumbranceDate)
                : '',
            encumbranceCert: parsedEditData?.encumbranceCert || null,

            // Property Tax Details
            propertyTaxNumber: parsedEditData?.propertyTaxNumber || '',
            propertyTaxDate: parsedEditData?.propertyTaxDate
                ? new Date(parsedEditData.propertyTaxDate)
                : '',
            propertyTaxRec: parsedEditData?.propertyTaxRec || null,

            siteId: siteId || null,
        },
        validationSchema: completeSiteSchema,
        validateOnChange: true,
        validateOnBlur: true,
        onSubmit: async (values) => {
            setIsSubmitting(true);
            const formPayload = new FormData();

            // Handle site images
            if (values.siteImages?.length) {
                values.siteImages.forEach((image) => {
                    if (image.uri && !image.uri.startsWith('http')) {
                        formPayload.append('siteImages', {
                            uri: image.uri,
                            name: image.name || 'siteImage.jpg',
                            type: image.mimeType || 'image/jpeg',
                        });
                    }
                });
            }

            // Handle encumbrance certificate
            if (
                values.encumbranceCert &&
                typeof values.encumbranceCert !== 'string' &&
                values.encumbranceCert.uri
            ) {
                formPayload.append('encumbranceCert', {
                    uri: values.encumbranceCert.uri,
                    name: values.encumbranceCert.name || 'encumbrance.pdf',
                    type: values.encumbranceCert.mimeType || 'application/pdf',
                });
            }

            // Handle property tax receipt
            if (
                values.propertyTaxRec &&
                typeof values.propertyTaxRec !== 'string' &&
                values.propertyTaxRec.uri
            ) {
                formPayload.append('propertyTaxRec', {
                    uri: values.propertyTaxRec.uri,
                    name: values.propertyTaxRec.name || 'propertyTax.pdf',
                    type: values.propertyTaxRec.mimeType || 'application/pdf',
                });
            }

            // Add other form fields
            const excludeFields = [
                'siteImages',
                'encumbranceCert',
                'propertyTaxRec',
                'siteId',
            ];
            Object.keys(values).forEach((key) => {
                if (!excludeFields.includes(key)) {
                    const value = values[key];
                    if (value !== null && value !== undefined && value !== '') {
                        if (value instanceof Date) {
                            formPayload.append(key, value.toISOString());
                        } else {
                            formPayload.append(key, value.toString());
                        }
                    }
                }
            });

            try {
                if (isEditing && siteId) {
                    await updateSite({ siteId, data: formPayload });
                } else {
                    await createSite(formPayload);
                }
            } catch (error) {
                console.error('Site submission error:', error);
            }
        },
    });

    const handleDocumentPreview = useCallback((documentUrl, documentType) => {
        setSelectedDocument({ url: documentUrl, type: documentType });
        setShowDocumentPreviewModal(true);
    }, []);

    const closeDocumentPreview = useCallback(() => {
        setShowDocumentPreviewModal(false);
        setSelectedDocument(null);
    }, []);

    if (isLoading) {
        return (
            <View
                style={[
                    styles.container,
                    { backgroundColor: theme.BACKGROUND },
                ]}
            >
                <LinearGradient
                    colors={[theme.PRIMARY, theme.SECONDARY]}
                    start={{ x: 0, y: 0 }}
                    end={{ x: 1, y: 1 }}
                    style={styles.backgroundContainer}
                />
                <View style={styles.contentContainer}>
                    <ActivityIndicator size="large" color={theme.PRIMARY} />
                    <Text
                        style={[
                            styles.subtitle,
                            { color: theme.WHITE, marginTop: 16 },
                        ]}
                    >
                        Loading...
                    </Text>
                </View>
            </View>
        );
    }

    if (isError) {
        return (
            <View
                style={[
                    styles.container,
                    { backgroundColor: theme.BACKGROUND },
                ]}
            >
                <LinearGradient
                    colors={[theme.PRIMARY, theme.SECONDARY]}
                    start={{ x: 0, y: 0 }}
                    end={{ x: 1, y: 1 }}
                    style={styles.backgroundContainer}
                />
                <View style={styles.contentContainer}>
                    <Text style={[styles.title, { color: theme.WHITE }]}>
                        Error
                    </Text>
                    <Text style={[styles.subtitle, { color: theme.WHITE }]}>
                        {error?.message || 'Failed to load user profile'}
                    </Text>
                </View>
            </View>
        );
    }

    const renderStepContent = () => {
        const stepProps = {
            theme,
            formik,
            setStep,
            isSubmitting: isSubmitting || isCreating || isUpdating,
            onDocumentPreview: handleDocumentPreview,
        };

        switch (step) {
            case 'siteDetails':
                return <SiteDetailsStep {...stepProps} />;
            case 'location':
                return <LocationStep {...stepProps} />;
            case 'encumbrance':
                return <EncumbranceStep {...stepProps} />;
            case 'propertyTax':
                return <PropertyTaxStep {...stepProps} />;
            case 'review':
                return <ReviewStep {...stepProps} />;
            default:
                return <SiteDetailsStep {...stepProps} />;
        }
    };

    return (
        <KeyboardAvoidingView
            behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
            style={[styles.container, { backgroundColor: theme.BACKGROUND }]}
        >
            <StatusBar
                barStyle={isDarkMode ? 'light-content' : 'dark-content'}
                backgroundColor={theme.PRIMARY}
            />

            {/* Background */}
            <View style={styles.backgroundContainer}>
                <Image
                    source={require('../../assets/images/background.png')}
                    style={styles.backgroundImage}
                    resizeMode="cover"
                />
                <LinearGradient
                    colors={[theme.PRIMARY, theme.SECONDARY]}
                    start={{ x: 0, y: 0 }}
                    end={{ x: 1, y: 1 }}
                    style={styles.backgroundOverlay}
                />
            </View>

            <BackButton color={theme.WHITE} />

            <ScrollView
                ref={scrollViewRef}
                contentContainerStyle={styles.scrollContainer}
                showsVerticalScrollIndicator={false}
                keyboardShouldPersistTaps="handled"
            >
                <View style={styles.contentContainer}>
                    <View
                        style={[
                            styles.formContainer,
                            {
                                backgroundColor: theme.CARD,
                                shadowColor: theme.PRIMARY,
                            },
                        ]}
                    >
                        <Text style={[styles.title, { color: theme.PRIMARY }]}>
                            {isEditing
                                ? 'Update Site Listing'
                                : 'List Your Site'}
                        </Text>
                        <Text
                            style={[
                                styles.subtitle,
                                { color: theme.TEXT_SECONDARY },
                            ]}
                        >
                            {isEditing
                                ? 'Update your site information'
                                : 'Provide details about your property'}
                        </Text>

                        {renderStepContent()}
                    </View>
                </View>
            </ScrollView>

            <DocumentPreviewModal
                visible={showDocumentPreviewModal}
                documentUrl={selectedDocument?.url}
                documentType={selectedDocument?.type}
                onClose={closeDocumentPreview}
            />
        </KeyboardAvoidingView>
    );
};

export default SiteForm;
