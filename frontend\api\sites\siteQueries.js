import { useQuery, useInfiniteQuery } from '@tanstack/react-query';
import { privateAPIClient } from '../index';

// Fetch all sites with filters
export const fetchSites = async (filters = {}) => {
    try {
        const query = new URLSearchParams();
        
        // Add filters to query params
        if (filters.search) query.append('search', filters.search);
        if (filters.state) query.append('state', filters.state);
        if (filters.district) query.append('district', filters.district);
        if (filters.pincode) query.append('pincode', filters.pincode);
        if (filters.latitude) query.append('latitude', filters.latitude);
        if (filters.longitude) query.append('longitude', filters.longitude);
        if (filters.distance) query.append('distance', filters.distance);
        if (filters.sortBy) query.append('sortBy', filters.sortBy);
        if (filters.order) query.append('order', filters.order);
        if (filters.page) query.append('page', filters.page);
        if (filters.limit) query.append('limit', filters.limit);

        const url = `/site-service/api/v1/sites?${query.toString()}`;
        const response = await privateAPIClient.get(url);
        return response.data;
    } catch (error) {
        console.error('Error fetching sites:', error);
        throw new Error(error.response?.data?.message || 'Failed to fetch sites');
    }
};

// Fetch single site by ID
export const fetchSiteById = async (siteId) => {
    try {
        const response = await privateAPIClient.get(`/site-service/api/v1/sites/${siteId}`);
        return response.data.site;
    } catch (error) {
        console.error('Error fetching site:', error);
        throw new Error(error.response?.data?.message || 'Failed to fetch site details');
    }
};

// Fetch user's sites
export const fetchUserSites = async (filters = {}) => {
    try {
        const query = new URLSearchParams();
        if (filters.page) query.append('page', filters.page);
        if (filters.limit) query.append('limit', filters.limit);
        if (filters.status) query.append('status', filters.status);

        const url = `/site-service/api/v1/sites/user?${query.toString()}`;
        const response = await privateAPIClient.get(url);
        return response.data;
    } catch (error) {
        console.error('Error fetching user sites:', error);
        throw new Error(error.response?.data?.message || 'Failed to fetch your sites');
    }
};

// React Query Hooks

// Hook for fetching sites with filters
export const useSites = (filters = {}, options = {}) => {
    return useQuery({
        queryKey: ['sites', filters],
        queryFn: () => fetchSites(filters),
        staleTime: 5 * 60 * 1000, // 5 minutes
        cacheTime: 10 * 60 * 1000, // 10 minutes
        ...options,
    });
};

// Hook for infinite scroll sites
export const useInfiniteSites = (filters = {}, options = {}) => {
    return useInfiniteQuery({
        queryKey: ['sites', 'infinite', filters],
        queryFn: ({ pageParam = 1 }) => fetchSites({ ...filters, page: pageParam }),
        getNextPageParam: (lastPage, allPages) => {
            const hasMore = lastPage.sites?.length === (filters.limit || 10);
            return hasMore ? allPages.length + 1 : undefined;
        },
        staleTime: 5 * 60 * 1000,
        cacheTime: 10 * 60 * 1000,
        ...options,
    });
};

// Hook for fetching single site
export const useSite = (siteId, options = {}) => {
    return useQuery({
        queryKey: ['site', siteId],
        queryFn: () => fetchSiteById(siteId),
        enabled: !!siteId,
        staleTime: 5 * 60 * 1000,
        cacheTime: 10 * 60 * 1000,
        ...options,
    });
};

// Hook for fetching user's sites
export const useUserSites = (filters = {}, options = {}) => {
    return useQuery({
        queryKey: ['userSites', filters],
        queryFn: () => fetchUserSites(filters),
        staleTime: 2 * 60 * 1000, // 2 minutes for user's own data
        cacheTime: 5 * 60 * 1000,
        ...options,
    });
};

// Hook for infinite scroll user sites
export const useInfiniteUserSites = (filters = {}, options = {}) => {
    return useInfiniteQuery({
        queryKey: ['userSites', 'infinite', filters],
        queryFn: ({ pageParam = 1 }) => fetchUserSites({ ...filters, page: pageParam }),
        getNextPageParam: (lastPage, allPages) => {
            const hasMore = lastPage.sites?.length === (filters.limit || 10);
            return hasMore ? allPages.length + 1 : undefined;
        },
        staleTime: 2 * 60 * 1000,
        cacheTime: 5 * 60 * 1000,
        ...options,
    });
};
