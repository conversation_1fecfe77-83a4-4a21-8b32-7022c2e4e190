import { privateAPIClient } from '../index';

// Fetch user's site profile/application
export const fetchSiteProfile = async () => {
    const url = '/site-service/api/v1/sites/profile';
    const response = await privateAPIClient.get(url);
    return response.data.site;
};

// Fetch all sites for listing/browsing
export const fetchAllSites = async (filters = {}) => {
    const query = new URLSearchParams();
    
    // Add filters to query params
    if (filters.search) query.append('search', filters.search);
    if (filters.state) query.append('state', filters.state);
    if (filters.district) query.append('district', filters.district);
    if (filters.pincode) query.append('pincode', filters.pincode);
    if (filters.latitude) query.append('latitude', filters.latitude);
    if (filters.longitude) query.append('longitude', filters.longitude);
    if (filters.distance) query.append('distance', filters.distance);
    if (filters.sortBy) query.append('sortBy', filters.sortBy);
    if (filters.order) query.append('order', filters.order);
    if (filters.page) query.append('page', filters.page);
    if (filters.limit) query.append('limit', filters.limit);

    const url = `/site-service/api/v1/sites?${query.toString()}`;
    const response = await privateAPIClient.get(url);
    return response.data.sites;
};

// Fetch individual site profile by ID
export const fetchSiteProfileById = async (siteId) => {
    const url = `/site-service/api/v1/sites/${siteId}`;
    const response = await privateAPIClient.get(url);
    return response.data.site;
};

// Create new site application
export const createSiteApplication = async (data) => {
    const response = await privateAPIClient.post(
        '/site-service/api/v1/sites',
        data,
        {
            headers: {
                'Content-Type': 'multipart/form-data',
            },
            transformRequest: (formData) => formData,
        }
    );
    return response.data;
};

// Update existing site profile
export const updateSiteProfile = async (id, data) => {
    const response = await privateAPIClient.patch(
        `/site-service/api/v1/sites/${id}`,
        data,
        {
            headers: {
                'Content-Type': 'multipart/form-data',
            },
            transformRequest: (formData) => formData,
        }
    );
    return response.data;
};

// Delete site listing
export const deleteSiteProfile = async (siteId) => {
    const response = await privateAPIClient.delete(
        `/site-service/api/v1/sites/${siteId}`
    );
    return response.data;
};

// Fetch user's sites
export const fetchUserSites = async (filters = {}) => {
    const query = new URLSearchParams();
    if (filters.page) query.append('page', filters.page);
    if (filters.limit) query.append('limit', filters.limit);
    if (filters.status) query.append('status', filters.status);

    const url = `/site-service/api/v1/sites/user?${query.toString()}`;
    const response = await privateAPIClient.get(url);
    return response.data;
};

// Update site status (admin function)
export const updateSiteStatus = async (siteId, status) => {
    const response = await privateAPIClient.patch(
        `/site-service/api/v1/sites/${siteId}/status`,
        { status }
    );
    return response.data;
};

// Search sites by location
export const searchSitesByLocation = async (latitude, longitude, radius = 10) => {
    const url = `/site-service/api/v1/sites/search/location?latitude=${latitude}&longitude=${longitude}&radius=${radius}`;
    const response = await privateAPIClient.get(url);
    return response.data.sites;
};

// Get site statistics
export const fetchSiteStatistics = async () => {
    const url = '/site-service/api/v1/sites/statistics';
    const response = await privateAPIClient.get(url);
    return response.data;
};

// Upload site images
export const uploadSiteImages = async (siteId, images) => {
    const formData = new FormData();
    images.forEach((image, index) => {
        formData.append('siteImages', {
            uri: image.uri,
            name: image.name || `image_${index}.jpg`,
            type: image.type || 'image/jpeg',
        });
    });

    const response = await privateAPIClient.post(
        `/site-service/api/v1/sites/${siteId}/images`,
        formData,
        {
            headers: {
                'Content-Type': 'multipart/form-data',
            },
        }
    );
    return response.data;
};

// Delete site image
export const deleteSiteImage = async (siteId, imageId) => {
    const response = await privateAPIClient.delete(
        `/site-service/api/v1/sites/${siteId}/images/${imageId}`
    );
    return response.data;
};

// Report site listing
export const reportSite = async (siteId, reason, description) => {
    const response = await privateAPIClient.post(
        `/site-service/api/v1/sites/${siteId}/report`,
        { reason, description }
    );
    return response.data;
};

// Bookmark/Favorite site
export const toggleSiteBookmark = async (siteId) => {
    const response = await privateAPIClient.post(
        `/site-service/api/v1/sites/${siteId}/bookmark`
    );
    return response.data;
};

// Get user's bookmarked sites
export const fetchBookmarkedSites = async () => {
    const url = '/site-service/api/v1/sites/bookmarks';
    const response = await privateAPIClient.get(url);
    return response.data.sites;
};
