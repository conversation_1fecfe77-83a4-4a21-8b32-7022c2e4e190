import { useState, useEffect } from 'react';
import { privateAPIClient } from '../index';
import { Alert } from 'react-native';

export const useSiteDetails = (siteId) => {
    const [fields, setFields] = useState({
        name: '',
        addressLine1: '',
        addressLine2: '',
        landmark: '',
        location: '',
        pincode: '',
        state: '',
        district: '',
        plotArea: '',
        price: '',
        latitude: '',
        longitude: '',
        encOwnerName: '',
        encDocumentNo: '',
        surveyNo: '',
        village: '',
        subDistrict: '',
        District: '',
        ptrOwnerName: '',
        ptrReciptNo: '',
        siteImages: [],
        encumbranceCert: null,
        propertyTaxRec: null,
    });

    const [isLoading, setIsLoading] = useState(false);

    const fetchSiteDetails = async () => {
        setIsLoading(true);
        try {
            const response = await privateAPIClient.get(
                `/site-service/api/v1/sites/${siteId}`
            );
            // Log the full response for debugging
            console.log(
                'API Response:',
                JSON.stringify(response.data, null, 2)
            );

            // Extract data from response
            const {
                site,
                encCertificate,
                ptReceipt,
                siteAssets,
                encAssets,
                ptrAsset,
            } = response.data;

            // Map assets to fields format, marking as existing to prevent UI display
            const siteImages = Array.isArray(siteAssets)
                ? siteAssets.map((asset) => ({
                      uri: asset.imageURL,
                      name: asset.name,
                      mimeType: 'existing', // Flag to indicate existing file
                      fileType: asset.fileType,
                  }))
                : [];

            const encumbranceCert =
                Array.isArray(encAssets) && encAssets.length > 0
                    ? {
                          uri: encAssets[0].imageURL,
                          name: encAssets[0].name,
                          mimeType: 'existing', // Flag to indicate existing file
                          fileType: encAssets[0].fileType,
                      }
                    : null;

            const propertyTaxRec =
                Array.isArray(ptrAsset) && ptrAsset.length > 0
                    ? {
                          uri: ptrAsset[0].imageURL,
                          name: ptrAsset[0].name,
                          mimeType: 'existing', // Flag to indicate existing file
                          fileType: ptrAsset[0].fileType,
                      }
                    : null;

            // Ensure all fields are populated, with fallback to empty strings or appropriate defaults
            const fields = {
                name: site?.name || '',
                addressLine1: site?.addressLine1 || '',
                addressLine2: site?.addressLine2 || '',
                landmark: site?.landmark || '',
                location: site?.location || '',
                pincode: site?.pincode || '',
                state: site?.state || '',
                district: site?.district || '',
                plotArea: site?.plotArea ? String(site.plotArea) : '', // Convert to string for input
                price: site?.price ? String(site.price) : '', // Convert to string for input
                latitude: site?.latitude ? String(site.latitude) : '',
                longitude: site?.longitude ? String(site.longitude) : '',
                encOwnerName: encCertificate?.encOwnerName || '',
                encDocumentNo: encCertificate?.encDocumentNo || '',
                surveyNo: encCertificate?.surveyNo || '',
                village: encCertificate?.village || '',
                subDistrict: encCertificate?.subDistrict || '',
                District: encCertificate?.District || '',
                ptrOwnerName: ptReceipt?.ptrOwnerName || '', // Optional
                ptrReciptNo: ptReceipt?.ptrReciptNo || '', // Optional
                siteImages, // Include fetched images, marked as existing
                encumbranceCert, // Include fetched encumbrance certificate, marked as existing
                propertyTaxRec, // Include fetched property tax receipt if available, marked as existing
            };

            setFields(fields);
        } catch (err) {
            console.error('Failed to fetch site details:', {
                message: err.message,
                status: err.response?.status,
                data: err.response?.data,
            });
            Alert.alert(
                'Error',
                `Failed to load site details: ${err.message}. Please try again.`
            );
        } finally {
            setIsLoading(false);
        }
    };

    useEffect(() => {
        if (siteId) {
            fetchSiteDetails();
        }
    }, [siteId]);

    return { fields, isLoading, setFields }; // Return fields and isLoading for use in components
};
