import React, { useState, useContext } from 'react';
import {
    View,
    Text,
    ScrollView,
    KeyboardAvoidingView,
    Platform,
    Image,
} from 'react-native';
import { LinearGradient } from 'expo-linear-gradient';
import { useRouter } from 'expo-router';
import { Formik } from 'formik';
import { ThemeContext } from '../../context/ThemeContext';
import BackButton from '../Components/Shared/BackButton';
import { styles } from './siteStyles';
import {
    useCreateSite,
    buildSiteFormData,
} from '../../api/sites/siteMutations';
import {
    initialSiteValues,
    getStepValidationSchema,
} from './validations/siteValidations';

// Import step components (to be created)
import SiteDetailsStep from './AddSites/SiteDetailsStep';
import LocationStep from './AddSites/LocationStep';
import EncumbranceStep from './AddSites/EncumbranceStep';
import PropertyTaxStep from './AddSites/PropertyTaxStep';
import ReviewStep from './AddSites/ReviewStep';

const AddSites = () => {
    const { theme } = useContext(ThemeContext);
    const router = useRouter();
    const [step, setStep] = useState('siteDetails');

    // React Query mutation for creating site
    const createSiteMutation = useCreateSite();

    // Map state
    const [region, setRegion] = useState({
        latitude: 37.78825,
        longitude: -122.4324,
        latitudeDelta: 0.0922,
        longitudeDelta: 0.0421,
    });

    const [marker, setMarker] = useState(null);
    const [hasPermission, setHasPermission] = useState(false);

    // Handle form submission
    const handleFormSubmit = async (values) => {
        try {
            const formData = buildSiteFormData(values);
            await createSiteMutation.mutateAsync(formData);
            router.replace('/Sites/SiteSuccess');
        } catch (error) {
            console.error('Site creation failed:', error);
        }
    };

    return (
        <Formik
            initialValues={initialSiteValues}
            validationSchema={getStepValidationSchema(step)}
            onSubmit={handleFormSubmit}
            enableReinitialize
        >
            {({
                values,
                setFieldValue,
                errors,
                touched,
                handleSubmit,
                validateForm,
            }) => (
                <KeyboardAvoidingView
                    behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
                    style={[
                        styles.container,
                        { backgroundColor: theme.BACKGROUND },
                    ]}
                >
                    <ScrollView
                        contentContainerStyle={styles.scrollContainer}
                        showsVerticalScrollIndicator={false}
                    >
                        <BackButton color={theme.WHITE} />

                        {/* Background Image */}
                        <View style={styles.backgroundContainer}>
                            <Image
                                source={require('../../assets/images/background.png')}
                                style={styles.backgroundImage}
                                resizeMode="cover"
                            />
                            <LinearGradient
                                colors={[
                                    'rgba(42, 142, 158, 0.7)',
                                    theme.PRIMARY,
                                ]}
                                style={styles.backgroundOverlay}
                                start={{ x: 0, y: 0 }}
                                end={{ x: 0, y: 1 }}
                            />
                        </View>

                        <View style={styles.contentContainer}>
                            <View
                                style={[
                                    styles.formContainer,
                                    {
                                        shadowColor: theme.SHADOW,
                                        backgroundColor: theme.CARD,
                                    },
                                ]}
                            >
                                <Text
                                    style={[
                                        styles.title,
                                        { color: theme.PRIMARY },
                                    ]}
                                >
                                    List Your Site
                                </Text>
                                <Text
                                    style={[
                                        styles.subtitle,
                                        { color: theme.TEXT_SECONDARY },
                                    ]}
                                >
                                    Add your property to Build Connect
                                </Text>
                                <Text
                                    style={[
                                        styles.subtitle,
                                        { color: theme.TEXT_SECONDARY },
                                    ]}
                                >
                                    Step{' '}
                                    {[
                                        'siteDetails',
                                        'location',
                                        'encumbrance',
                                        'propertyTax',
                                        'review',
                                    ].indexOf(step) + 1}{' '}
                                    of 5
                                </Text>

                                {step === 'siteDetails' && (
                                    <SiteDetailsStep
                                        theme={theme}
                                        values={values}
                                        setFieldValue={setFieldValue}
                                        errors={errors}
                                        touched={touched}
                                        setStep={setStep}
                                        validateForm={validateForm}
                                    />
                                )}
                                {step === 'location' && (
                                    <LocationStep
                                        theme={theme}
                                        values={values}
                                        setFieldValue={setFieldValue}
                                        errors={errors}
                                        touched={touched}
                                        region={region}
                                        setRegion={setRegion}
                                        marker={marker}
                                        setMarker={setMarker}
                                        hasPermission={hasPermission}
                                        setHasPermission={setHasPermission}
                                        setStep={setStep}
                                        validateForm={validateForm}
                                    />
                                )}
                                {step === 'encumbrance' && (
                                    <EncumbranceStep
                                        theme={theme}
                                        values={values}
                                        setFieldValue={setFieldValue}
                                        errors={errors}
                                        touched={touched}
                                        setStep={setStep}
                                        validateForm={validateForm}
                                    />
                                )}
                                {step === 'propertyTax' && (
                                    <PropertyTaxStep
                                        theme={theme}
                                        values={values}
                                        setFieldValue={setFieldValue}
                                        errors={errors}
                                        touched={touched}
                                        setStep={setStep}
                                        validateForm={validateForm}
                                    />
                                )}
                                {step === 'review' && (
                                    <ReviewStep
                                        theme={theme}
                                        values={values}
                                        setStep={setStep}
                                        handleSubmit={handleSubmit}
                                        isSubmitting={
                                            createSiteMutation.isLoading
                                        }
                                    />
                                )}
                            </View>
                        </View>
                    </ScrollView>
                </KeyboardAvoidingView>
            )}
        </Formik>
    );
};

export default AddSites;
